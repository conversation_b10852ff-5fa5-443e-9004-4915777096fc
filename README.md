# VFD Clock V16 - 模块化重构版本

## 项目结构

```
V16/
├── include/                    # 头文件目录
│   ├── config.h               # 硬件配置和常量定义
│   ├── vfd_driver.h           # VFD显示驱动模块
│   ├── rtc.h                  # RTC时间控制模块
│   ├── clock_display.h        # 时钟显示管理模块
│   ├── buttons.h              # 按键处理模块
│   ├── utils.h                # 工具函数模块
│   ├── wifi_manager.h         # WiFi连接管理模块
│   ├── ntp_client.h           # NTP时间同步模块
│   ├── timezone.h             # 时区和夏令时管理模块
│   ├── settings.h             # 设置存储管理模块
│   └── web_server.h           # Web配置界面模块
├── src/                       # 源文件目录
│   ├── main.cpp               # 主程序文件
│   ├── vfd_driver.cpp         # VFD显示驱动实现
│   ├── rtc.cpp                # RTC时间控制实现
│   ├── clock_display.cpp      # 时钟显示管理实现
│   ├── buttons.cpp            # 按键处理实现
│   ├── utils.cpp              # 工具函数实现
│   ├── wifi_manager.cpp       # WiFi连接管理实现
│   ├── ntp_client.cpp         # NTP时间同步实现
│   ├── timezone.cpp           # 时区和夏令时管理实现
│   ├── settings.cpp           # 设置存储管理实现
│   └── web_server.cpp         # Web配置界面实现
├── lib/                       # 第三方库目录
├── platformio.ini             # PlatformIO配置文件
└── README.md                  # 项目说明文档
```

## 模块说明

### 1. config.h - 硬件配置模块
- 定义所有硬件引脚
- 定义系统常量
- 集中管理硬件配置

### 2. vfd_driver - VFD显示驱动模块
- VFDDriver类：封装VFD显示器的所有操作
- 支持字符串显示、亮度调节、清屏等功能
- 提供简洁的API接口

### 3. rtc - RTC时间控制模块
- RTCController类：管理实时时钟
- TimeData结构：标准化时间数据格式
- 支持时间读取、设置和状态检查

### 4. clock_display - 时钟显示管理模块
- ClockDisplay类：管理时钟显示逻辑
- 支持时间模式和日期模式切换
- 自动更新显示和消息显示功能

### 5. buttons - 按键处理模块
- ButtonHandler类：处理所有按键操作
- 支持防抖、状态检测
- 模块化的按键功能分配

### 6. utils - 工具函数模块
- BCD转换函数
- 字符串格式化函数
- 通用工具函数

### 7. wifi_manager - WiFi连接管理模块
- WiFiManager类：管理WiFi连接和配置
- 支持自动连接、重连机制
- AP配置模式和网络扫描功能

### 8. ntp_client - NTP时间同步模块
- NTPClient类：网络时间协议客户端
- 多服务器支持和自动切换
- 定时同步和错误处理

### 9. timezone - 时区和夏令时管理模块
- TimezoneManager类：时区转换和管理
- 支持预定义时区和自定义时区
- 自动夏令时计算和转换

### 10. settings - 设置存储管理模块
- SettingsManager类：配置持久化存储
- 使用ESP32 Preferences库
- 设置验证和版本迁移

### 11. web_server - Web配置界面模块
- WebServer类：HTTP服务器和配置界面
- RESTful API接口
- OTA固件更新支持

## 功能特性

- **模块化设计**：每个功能模块独立，便于维护和扩展
- **面向对象**：使用C++类封装，提供清晰的API
- **标准化接口**：统一的函数命名和参数规范
- **错误处理**：完善的错误检查和状态反馈
- **可扩展性**：预留接口便于添加新功能

## 按键功能

- **按键1**：亮度调节（4级亮度循环）
- **按键2**：显示模式切换（时间/日期）
- **按键3**：预留功能（可扩展）

## 编译和上传

使用PlatformIO进行编译和上传：

```bash
# 编译
pio run

# 上传到设备
pio run --target upload

# 串口监视器
pio device monitor
```

## 硬件连接

- **RTC模块**：SDA(18), SCL(19)
- **VFD显示器**：CLK(3), DIN(5), CS(8)
- **按键**：BUTTON1(9), BUTTON2(2), BUTTON3(10)
- **蜂鸣器**：BEEP(4)

## 版本历史

- **V16**: 模块化重构版本，提高代码可维护性和可扩展性
