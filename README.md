# VFD Clock V16 - 模块化重构版本

## 项目结构

```
V16/
├── include/                    # 头文件目录
│   ├── config.h               # 硬件配置和常量定义
│   ├── vfd_driver.h           # VFD显示驱动模块
│   ├── rtc.h                  # RTC时间控制模块
│   ├── clock_display.h        # 时钟显示管理模块
│   ├── buttons.h              # 按键处理模块
│   └── utils.h                # 工具函数模块
├── src/                       # 源文件目录
│   ├── main.cpp               # 主程序文件
│   ├── vfd_driver.cpp         # VFD显示驱动实现
│   ├── rtc.cpp                # RTC时间控制实现
│   ├── clock_display.cpp      # 时钟显示管理实现
│   ├── buttons.cpp            # 按键处理实现
│   └── utils.cpp              # 工具函数实现
├── lib/                       # 第三方库目录
└── platformio.ini             # PlatformIO配置文件
```

## 模块说明

### 1. config.h - 硬件配置模块
- 定义所有硬件引脚
- 定义系统常量
- 集中管理硬件配置

### 2. vfd_driver - VFD显示驱动模块
- VFDDriver类：封装VFD显示器的所有操作
- 支持字符串显示、亮度调节、清屏等功能
- 提供简洁的API接口

### 3. rtc - RTC时间控制模块
- RTCController类：管理实时时钟
- TimeData结构：标准化时间数据格式
- 支持时间读取、设置和状态检查

### 4. clock_display - 时钟显示管理模块
- ClockDisplay类：管理时钟显示逻辑
- 支持时间模式和日期模式切换
- 自动更新显示和消息显示功能

### 5. buttons - 按键处理模块
- ButtonHandler类：处理所有按键操作
- 支持防抖、状态检测
- 模块化的按键功能分配

### 6. utils - 工具函数模块
- BCD转换函数
- 字符串格式化函数
- 通用工具函数

## 功能特性

- **模块化设计**：每个功能模块独立，便于维护和扩展
- **面向对象**：使用C++类封装，提供清晰的API
- **标准化接口**：统一的函数命名和参数规范
- **错误处理**：完善的错误检查和状态反馈
- **可扩展性**：预留接口便于添加新功能

## 按键功能

- **按键1**：亮度调节（4级亮度循环）
- **按键2**：显示模式切换（时间/日期）
- **按键3**：预留功能（可扩展）

## 编译和上传

使用PlatformIO进行编译和上传：

```bash
# 编译
pio run

# 上传到设备
pio run --target upload

# 串口监视器
pio device monitor
```

## 硬件连接

- **RTC模块**：SDA(18), SCL(19)
- **VFD显示器**：CLK(3), DIN(5), CS(8)
- **按键**：BUTTON1(9), BUTTON2(2), BUTTON3(10)
- **蜂鸣器**：BEEP(4)

## 版本历史

- **V16**: 模块化重构版本，提高代码可维护性和可扩展性
