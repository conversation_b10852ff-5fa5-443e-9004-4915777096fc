/**
 * VFD Clock - VFD Driver Module
 * VFD显示驱动模块
 */

#ifndef VFD_DRIVER_H
#define VFD_DRIVER_H

#include <Arduino.h>

/**
 * VFD驱动类
 */
class VFDDriver {
private:
    uint8_t brightness;
    
    /**
     * VFD延时函数
     */
    void vfdDelay();
    
    /**
     * 写入一个字节到VFD
     * @param data 要写入的数据
     */
    void vfdWrite(uint8_t data);
    
    /**
     * 发送命令到VFD
     * @param cmd 命令字节
     */
    void vfdCmd(uint8_t cmd);

public:
    /**
     * 构造函数
     */
    VFDDriver();
    
    /**
     * 初始化VFD
     */
    void init();
    
    /**
     * 显示字符串
     * @param str 要显示的字符串
     */
    void display(const char* str);
    
    /**
     * 设置亮度
     * @param level 亮度等级 (0-3)
     */
    void setBrightness(uint8_t level);
    
    /**
     * 获取当前亮度等级
     * @return 当前亮度等级
     */
    uint8_t getBrightness() const;
    
    /**
     * 清屏
     */
    void clear();
};

#endif // VFD_DRIVER_H
