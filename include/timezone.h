/**
 * VFD Clock - Timezone Manager Module
 * 时区管理和夏令时处理模块
 */

#ifndef TIMEZONE_H
#define TIMEZONE_H

#include <Arduino.h>
#include "rtc.h"

/**
 * 夏令时规则结构
 */
struct DSTRule {
    uint8_t month;          // 月份 (1-12)
    uint8_t week;           // 第几周 (1-5, 5表示最后一周)
    uint8_t dayOfWeek;      // 星期几 (0=周日, 1=周一, ...)
    uint8_t hour;           // 小时 (0-23)
    int16_t offset;         // 时间偏移(分钟)
};

/**
 * 时区配置结构
 */
struct TimezoneConfig {
    char name[32];          // 时区名称
    char abbreviation[8];   // 时区缩写
    int16_t offsetMinutes;  // UTC偏移(分钟)
    bool dstEnabled;        // 是否启用夏令时
    DSTRule dstStart;       // 夏令时开始规则
    DSTRule dstEnd;         // 夏令时结束规则
    int16_t dstOffset;      // 夏令时偏移(分钟，通常是60)
};

/**
 * 预定义时区枚举
 */
enum PredefinedTimezone {
    TZ_UTC = 0,
    TZ_BEIJING,             // 北京时间 UTC+8
    TZ_TOKYO,               // 东京时间 UTC+9
    TZ_NEW_YORK,            // 纽约时间 UTC-5/-4 (EST/EDT)
    TZ_LOS_ANGELES,         // 洛杉矶时间 UTC-8/-7 (PST/PDT)
    TZ_LONDON,              // 伦敦时间 UTC+0/+1 (GMT/BST)
    TZ_PARIS,               // 巴黎时间 UTC+1/+2 (CET/CEST)
    TZ_SYDNEY,              // 悉尼时间 UTC+10/+11 (AEST/AEDT)
    TZ_CUSTOM               // 自定义时区
};

/**
 * 时区管理类
 */
class TimezoneManager {
private:
    TimezoneConfig currentTimezone;
    bool isDSTActive;
    
    /**
     * 计算指定年份的夏令时日期
     * @param year 年份
     * @param rule 夏令时规则
     * @return 夏令时日期的时间戳
     */
    time_t calculateDSTDate(int year, const DSTRule& rule);
    
    /**
     * 检查指定时间是否在夏令时期间
     * @param timestamp Unix时间戳
     * @return 是否在夏令时期间
     */
    bool isDSTTime(time_t timestamp);
    
    /**
     * 加载预定义时区配置
     * @param tz 预定义时区
     */
    void loadPredefinedTimezone(PredefinedTimezone tz);

public:
    /**
     * 构造函数
     */
    TimezoneManager();
    
    /**
     * 初始化时区管理器
     */
    void init();
    
    /**
     * 设置时区
     * @param tz 预定义时区
     */
    void setTimezone(PredefinedTimezone tz);
    
    /**
     * 设置自定义时区
     * @param name 时区名称
     * @param offsetMinutes UTC偏移(分钟)
     * @param dstEnabled 是否启用夏令时
     */
    void setCustomTimezone(const char* name, int16_t offsetMinutes, bool dstEnabled = false);
    
    /**
     * 设置夏令时规则
     * @param startRule 夏令时开始规则
     * @param endRule 夏令时结束规则
     * @param dstOffset 夏令时偏移(分钟)
     */
    void setDSTRules(const DSTRule& startRule, const DSTRule& endRule, int16_t dstOffset = 60);
    
    /**
     * 将UTC时间转换为本地时间
     * @param utcTime UTC时间
     * @return 本地时间
     */
    TimeData utcToLocal(const TimeData& utcTime);
    
    /**
     * 将本地时间转换为UTC时间
     * @param localTime 本地时间
     * @return UTC时间
     */
    TimeData localToUtc(const TimeData& localTime);
    
    /**
     * 获取当前时区配置
     */
    const TimezoneConfig& getTimezoneConfig() const;
    
    /**
     * 获取当前UTC偏移(包含夏令时)
     * @return 偏移分钟数
     */
    int16_t getCurrentOffset();
    
    /**
     * 检查当前是否为夏令时
     */
    bool isDSTActive() const;
    
    /**
     * 获取时区名称
     */
    const char* getTimezoneName() const;
    
    /**
     * 获取时区缩写
     */
    const char* getTimezoneAbbreviation() const;
    
    /**
     * 更新夏令时状态（定期调用）
     */
    void updateDSTStatus();
    
    /**
     * 格式化时区信息字符串
     * @param buffer 输出缓冲区
     * @param size 缓冲区大小
     */
    void formatTimezoneInfo(char* buffer, size_t size);
    
    /**
     * 获取可用时区列表
     * @param index 时区索引
     * @return 时区名称，如果索引无效返回nullptr
     */
    static const char* getAvailableTimezone(int index);
    
    /**
     * 获取可用时区数量
     */
    static int getAvailableTimezoneCount();
};

#endif // TIMEZONE_H
