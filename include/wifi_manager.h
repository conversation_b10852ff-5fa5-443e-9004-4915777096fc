/**
 * VFD Clock - WiFi Manager Module
 * WiFi连接和配置管理模块
 */

#ifndef WIFI_MANAGER_H
#define WIFI_MANAGER_H

#include <Arduino.h>
#include <WiFi.h>
#include <WiFiAP.h>

/**
 * WiFi连接状态枚举
 */
enum WiFiStatus {
    WIFI_DISCONNECTED = 0,
    WIFI_CONNECTING = 1,
    WIFI_CONNECTED = 2,
    WIFI_FAILED = 3,
    WIFI_AP_MODE = 4
};

/**
 * WiFi配置结构
 */
struct WiFiConfig {
    char ssid[32];
    char password[64];
    bool autoConnect;
    uint32_t connectTimeout;
};

/**
 * WiFi管理类
 */
class WiFiManager {
private:
    WiFiConfig config;
    WiFiStatus currentStatus;
    unsigned long lastConnectAttempt;
    unsigned long connectStartTime;
    uint8_t reconnectAttempts;
    bool apModeActive;
    
    /**
     * 尝试连接到WiFi
     */
    void attemptConnection();
    
    /**
     * 启动AP模式
     */
    void startAPMode();
    
    /**
     * 停止AP模式
     */
    void stopAPMode();

public:
    /**
     * 构造函数
     */
    WiFiManager();
    
    /**
     * 初始化WiFi管理器
     */
    void init();
    
    /**
     * 更新WiFi状态（在主循环中调用）
     */
    void update();
    
    /**
     * 设置WiFi配置
     * @param ssid WiFi名称
     * @param password WiFi密码
     * @param autoConnect 是否自动连接
     */
    void setConfig(const char* ssid, const char* password, bool autoConnect = true);
    
    /**
     * 开始连接WiFi
     */
    void connect();
    
    /**
     * 断开WiFi连接
     */
    void disconnect();
    
    /**
     * 获取当前WiFi状态
     */
    WiFiStatus getStatus() const;
    
    /**
     * 获取WiFi配置
     */
    const WiFiConfig& getConfig() const;
    
    /**
     * 检查是否已连接
     */
    bool isConnected() const;
    
    /**
     * 获取IP地址
     */
    String getIPAddress() const;
    
    /**
     * 获取信号强度
     */
    int32_t getRSSI() const;
    
    /**
     * 启动配置模式（AP模式）
     */
    void startConfigMode();
    
    /**
     * 停止配置模式
     */
    void stopConfigMode();
    
    /**
     * 检查是否在配置模式
     */
    bool isInConfigMode() const;
    
    /**
     * 扫描可用WiFi网络
     */
    int scanNetworks();
    
    /**
     * 获取扫描到的网络信息
     */
    String getScannedSSID(int index) const;
    int32_t getScannedRSSI(int index) const;
    bool isScannedNetworkSecure(int index) const;
};

#endif // WIFI_MANAGER_H
