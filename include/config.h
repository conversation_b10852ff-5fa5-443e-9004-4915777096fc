/**
 * VFD Clock - Hardware Configuration
 * 硬件配置文件
 */

#ifndef CONFIG_H
#define CONFIG_H

#include <Arduino.h>

// 硬件引脚定义
#define RTC_SDA_PIN 18
#define RTC_SCL_PIN 19
#define RTC_ADDRESS 0x32

#define VFD_CLK_PIN 3
#define VFD_DIN_PIN 5
#define VFD_CS_PIN  8

#define BUTTON1_PIN 9
#define BUTTON2_PIN 2
#define BUTTON3_PIN 10

#define BEEP_PIN    4

// 显示配置
#define DISPLAY_WIDTH 16
#define BRIGHTNESS_LEVELS 4

// 时间配置
#define UPDATE_INTERVAL 1000  // 显示更新间隔(ms)
#define BUTTON_DEBOUNCE 300   // 按键防抖时间(ms)

// 亮度等级
extern const uint8_t brightLevels[BRIGHTNESS_LEVELS];

#endif // CONFIG_H
