/**
 * VFD Clock - NTP Client Module
 * NTP网络时间同步模块
 */

#ifndef NTP_CLIENT_H
#define NTP_CLIENT_H

#include <Arduino.h>
#include <WiFiUdp.h>
#include "rtc.h"

/**
 * NTP同步状态枚举
 */
enum NTPStatus {
    NTP_IDLE = 0,
    NTP_REQUESTING = 1,
    NTP_SUCCESS = 2,
    NTP_FAILED = 3,
    NTP_TIMEOUT = 4
};

/**
 * NTP服务器配置
 */
struct NTPConfig {
    char primaryServer[64];
    char secondaryServer[64];
    char tertiaryServer[64];
    uint16_t port;
    uint32_t syncInterval;      // 同步间隔(秒)
    uint32_t requestTimeout;    // 请求超时(毫秒)
    bool autoSync;              // 自动同步
};

/**
 * NTP时间数据
 */
struct NTPTime {
    uint32_t timestamp;         // Unix时间戳
    uint32_t fraction;          // 时间分数部分
    bool valid;                 // 时间是否有效
    unsigned long receivedAt;   // 接收时间(millis)
};

/**
 * NTP客户端类
 */
class NTPClient {
private:
    WiFiUDP udp;
    NTPConfig config;
    NTPStatus currentStatus;
    NTPTime lastSyncTime;
    unsigned long lastSyncAttempt;
    unsigned long requestStartTime;
    uint8_t currentServerIndex;
    uint8_t retryCount;
    
    /**
     * 发送NTP请求
     * @param serverName NTP服务器地址
     * @return 发送是否成功
     */
    bool sendNTPRequest(const char* serverName);
    
    /**
     * 解析NTP响应
     * @param ntpTime 输出的NTP时间
     * @return 解析是否成功
     */
    bool parseNTPResponse(NTPTime& ntpTime);
    
    /**
     * 获取当前使用的服务器地址
     */
    const char* getCurrentServer();
    
    /**
     * 切换到下一个服务器
     */
    void switchToNextServer();
    
    /**
     * 将NTP时间转换为TimeData
     */
    TimeData ntpToTimeData(const NTPTime& ntpTime);

public:
    /**
     * 构造函数
     */
    NTPClient();
    
    /**
     * 初始化NTP客户端
     */
    void init();
    
    /**
     * 更新NTP状态（在主循环中调用）
     */
    void update();
    
    /**
     * 设置NTP配置
     * @param primary 主NTP服务器
     * @param secondary 备用NTP服务器
     * @param tertiary 第三NTP服务器
     * @param syncInterval 同步间隔(秒)
     */
    void setConfig(const char* primary, const char* secondary = nullptr, 
                   const char* tertiary = nullptr, uint32_t syncInterval = 3600);
    
    /**
     * 开始时间同步
     * @return 请求是否发送成功
     */
    bool syncTime();
    
    /**
     * 强制同步时间
     */
    void forceSyncTime();
    
    /**
     * 获取当前状态
     */
    NTPStatus getStatus() const;
    
    /**
     * 获取最后同步的时间
     */
    const NTPTime& getLastSyncTime() const;
    
    /**
     * 检查是否需要同步
     */
    bool needsSync() const;
    
    /**
     * 获取当前网络时间
     * @param timeData 输出的时间数据
     * @return 获取是否成功
     */
    bool getCurrentTime(TimeData& timeData);
    
    /**
     * 设置自动同步
     */
    void setAutoSync(bool enable);
    
    /**
     * 获取同步间隔
     */
    uint32_t getSyncInterval() const;
    
    /**
     * 设置同步间隔
     */
    void setSyncInterval(uint32_t interval);
    
    /**
     * 获取上次同步时间距离现在的秒数
     */
    uint32_t getTimeSinceLastSync() const;
    
    /**
     * 检查时间是否有效
     */
    bool isTimeValid() const;
};

#endif // NTP_CLIENT_H
