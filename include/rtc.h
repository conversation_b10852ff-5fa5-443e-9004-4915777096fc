/**
 * VFD Clock - RTC Module
 * 实时时钟模块
 */

#ifndef RTC_H
#define RTC_H

#include <Arduino.h>

/**
 * 时间数据结构
 */
struct TimeData {
    uint8_t hour;
    uint8_t minute;
    uint8_t second;
    uint8_t date;
    uint8_t month;
    uint8_t year;
    uint8_t dayOfWeek;
};

/**
 * RTC控制类
 */
class RTCController {
private:
    TimeData currentTime;
    
public:
    /**
     * 构造函数
     */
    RTCController();
    
    /**
     * 初始化RTC
     * @return 初始化是否成功
     */
    bool init();
    
    /**
     * 读取时间
     * @return 读取是否成功
     */
    bool readTime();
    
    /**
     * 设置时间
     * @param time 要设置的时间
     * @return 设置是否成功
     */
    bool setTime(const TimeData& time);
    
    /**
     * 获取当前时间
     * @return 当前时间数据
     */
    const TimeData& getTime() const;
    
    /**
     * 检查RTC是否正常工作
     * @return RTC状态
     */
    bool isRunning();
};

#endif // RTC_H
