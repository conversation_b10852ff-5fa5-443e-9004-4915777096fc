/**
 * VFD Clock - Clock Display Module
 * 时钟显示模块
 */

#ifndef CLOCK_DISPLAY_H
#define CLOCK_DISPLAY_H

#include <Arduino.h>
#include "rtc.h"
#include "vfd_driver.h"

/**
 * 显示模式枚举
 */
enum DisplayMode {
    MODE_TIME = 0,    // 时间模式
    MODE_DATE = 1     // 日期模式
};

/**
 * 时钟显示控制类
 */
class ClockDisplay {
private:
    VFDDriver* vfd;
    RTCController* rtc;
    DisplayMode currentMode;
    unsigned long lastUpdate;
    
public:
    /**
     * 构造函数
     * @param vfdDriver VFD驱动器指针
     * @param rtcController RTC控制器指针
     */
    ClockDisplay(VFDDriver* vfdDriver, RTCController* rtcController);
    
    /**
     * 初始化显示
     */
    void init();
    
    /**
     * 更新显示
     * @param forceUpdate 是否强制更新
     */
    void update(bool forceUpdate = false);
    
    /**
     * 切换显示模式
     */
    void toggleMode();
    
    /**
     * 设置显示模式
     * @param mode 显示模式
     */
    void setMode(DisplayMode mode);
    
    /**
     * 获取当前显示模式
     * @return 当前显示模式
     */
    DisplayMode getMode() const;
    
    /**
     * 显示启动信息
     */
    void showStartupMessage();
    
    /**
     * 显示自定义消息
     * @param message 要显示的消息
     * @param duration 显示持续时间(ms)
     */
    void showMessage(const char* message, unsigned long duration = 2000);
};

#endif // CLOCK_DISPLAY_H
