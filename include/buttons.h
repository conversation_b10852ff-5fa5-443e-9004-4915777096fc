/**
 * VFD Clock - Button Handler Module
 * 按键处理模块
 */

#ifndef BUTTONS_H
#define BUTTONS_H

#include <Arduino.h>
#include "vfd_driver.h"
#include "clock_display.h"

/**
 * 按键类型枚举
 */
enum ButtonType {
    BUTTON_1 = 0,  // 亮度调节按键
    BUTTON_2 = 1,  // 模式切换按键
    BUTTON_3 = 2,  // 预留按键
    BUTTON_COUNT = 3
};

/**
 * 按键状态枚举
 */
enum ButtonState {
    BUTTON_RELEASED = 0,
    BUTTON_PRESSED = 1,
    BUTTON_HELD = 2
};

/**
 * 按键处理类
 */
class ButtonHandler {
private:
    VFDDriver* vfd;
    ClockDisplay* display;
    unsigned long lastPress[BUTTON_COUNT];
    ButtonState buttonStates[BUTTON_COUNT];
    
    /**
     * 读取按键状态
     * @param button 按键类型
     * @return 按键是否被按下
     */
    bool readButton(ButtonType button);
    
    /**
     * 处理亮度调节按键
     */
    void handleBrightnessButton();
    
    /**
     * 处理模式切换按键
     */
    void handleModeButton();
    
    /**
     * 处理预留按键
     */
    void handleReservedButton();

public:
    /**
     * 构造函数
     * @param vfdDriver VFD驱动器指针
     * @param clockDisplay 时钟显示器指针
     */
    ButtonHandler(VFDDriver* vfdDriver, ClockDisplay* clockDisplay);
    
    /**
     * 初始化按键
     */
    void init();
    
    /**
     * 检查并处理按键
     */
    void check();
    
    /**
     * 获取按键状态
     * @param button 按键类型
     * @return 按键状态
     */
    ButtonState getButtonState(ButtonType button) const;
};

#endif // BUTTONS_H
