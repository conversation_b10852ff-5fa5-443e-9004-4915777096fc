/**
 * VFD Clock - Settings Manager Module
 * 设置管理和存储模块
 */

#ifndef SETTINGS_H
#define SETTINGS_H

#include <Arduino.h>
#include <Preferences.h>
#include "wifi_manager.h"
#include "timezone.h"

/**
 * 系统设置结构
 */
struct SystemSettings {
    // 显示设置
    uint8_t brightness;
    uint8_t displayMode;
    bool autoDisplayMode;
    uint16_t displayTimeout;
    
    // WiFi设置
    WiFiConfig wifiConfig;
    
    // 时间设置
    char ntpServer1[64];
    char ntpServer2[64];
    char ntpServer3[64];
    uint32_t ntpSyncInterval;
    bool autoTimeSync;
    
    // 时区设置
    PredefinedTimezone timezone;
    TimezoneConfig customTimezone;
    
    // 系统设置
    bool serialDebug;
    uint16_t webServerPort;
    char deviceName[32];
    
    // 版本信息
    uint16_t settingsVersion;
    uint32_t lastModified;
};

/**
 * 设置管理类
 */
class SettingsManager {
private:
    Preferences preferences;
    SystemSettings currentSettings;
    bool settingsLoaded;
    bool settingsChanged;
    unsigned long lastSaveTime;
    
    /**
     * 加载默认设置
     */
    void loadDefaults();
    
    /**
     * 验证设置有效性
     */
    bool validateSettings();
    
    /**
     * 迁移旧版本设置
     */
    void migrateSettings(uint16_t oldVersion);

public:
    /**
     * 构造函数
     */
    SettingsManager();
    
    /**
     * 析构函数
     */
    ~SettingsManager();
    
    /**
     * 初始化设置管理器
     */
    bool init();
    
    /**
     * 加载设置
     */
    bool loadSettings();
    
    /**
     * 保存设置
     * @param force 是否强制保存
     */
    bool saveSettings(bool force = false);
    
    /**
     * 重置为默认设置
     */
    void resetToDefaults();
    
    /**
     * 获取当前设置
     */
    const SystemSettings& getSettings() const;
    
    /**
     * 更新设置（标记为已修改）
     */
    void updateSettings(const SystemSettings& settings);
    
    // 显示设置
    void setBrightness(uint8_t brightness);
    uint8_t getBrightness() const;
    
    void setDisplayMode(uint8_t mode);
    uint8_t getDisplayMode() const;
    
    void setAutoDisplayMode(bool enable);
    bool getAutoDisplayMode() const;
    
    // WiFi设置
    void setWiFiConfig(const WiFiConfig& config);
    const WiFiConfig& getWiFiConfig() const;
    
    // NTP设置
    void setNTPServers(const char* server1, const char* server2 = nullptr, const char* server3 = nullptr);
    void setNTPSyncInterval(uint32_t interval);
    uint32_t getNTPSyncInterval() const;
    
    void setAutoTimeSync(bool enable);
    bool getAutoTimeSync() const;
    
    // 时区设置
    void setTimezone(PredefinedTimezone tz);
    PredefinedTimezone getTimezone() const;
    
    void setCustomTimezone(const TimezoneConfig& config);
    const TimezoneConfig& getCustomTimezone() const;
    
    // 系统设置
    void setSerialDebug(bool enable);
    bool getSerialDebug() const;
    
    void setWebServerPort(uint16_t port);
    uint16_t getWebServerPort() const;
    
    void setDeviceName(const char* name);
    const char* getDeviceName() const;
    
    /**
     * 检查设置是否已修改
     */
    bool hasChanges() const;
    
    /**
     * 标记设置已修改
     */
    void markChanged();
    
    /**
     * 定期更新（在主循环中调用）
     */
    void update();
    
    /**
     * 导出设置为JSON字符串
     */
    String exportToJSON() const;
    
    /**
     * 从JSON字符串导入设置
     */
    bool importFromJSON(const String& json);
    
    /**
     * 获取设置摘要信息
     */
    void getSettingsSummary(char* buffer, size_t size) const;
    
    /**
     * 清除所有存储的设置
     */
    void clearAllSettings();
};

#endif // SETTINGS_H
