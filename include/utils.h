/**
 * VFD Clock - Utility Functions
 * 工具函数模块
 */

#ifndef UTILS_H
#define UTILS_H

#include <Arduino.h>

/**
 * BCD转十进制
 * @param val BCD值
 * @return 十进制值
 */
uint8_t bcdToDec(uint8_t val);

/**
 * 十进制转BCD
 * @param val 十进制值
 * @return BCD值
 */
uint8_t decToBcd(uint8_t val);

/**
 * 格式化时间字符串
 * @param buf 输出缓冲区
 * @param hour 小时
 * @param minute 分钟
 * @param second 秒
 */
void formatTimeString(char* buf, uint8_t hour, uint8_t minute, uint8_t second);

/**
 * 格式化日期字符串
 * @param buf 输出缓冲区
 * @param hour 小时
 * @param minute 分钟
 * @param date 日期
 * @param month 月份
 */
void formatDateString(char* buf, uint8_t hour, uint8_t minute, uint8_t date, uint8_t month);

#endif // UTILS_H
