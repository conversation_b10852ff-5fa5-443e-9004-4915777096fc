/**
 * VFD Clock - WiFi Manager Implementation
 * WiFi连接和配置管理实现
 */

#include "wifi_manager.h"
#include "config.h"

// WiFi配置常量
#define WIFI_CONNECT_TIMEOUT 30000  // 30秒连接超时
#define WIFI_RECONNECT_INTERVAL 60000  // 60秒重连间隔
#define MAX_RECONNECT_ATTEMPTS 5
#define AP_SSID "VFD-Clock-Config"
#define AP_PASSWORD "12345678"

WiFiManager::WiFiManager() 
    : currentStatus(WIFI_DISCONNECTED), lastConnectAttempt(0), 
      connectStartTime(0), reconnectAttempts(0), apModeActive(false) {
    
    // 初始化配置
    memset(&config, 0, sizeof(WiFiConfig));
    config.autoConnect = true;
    config.connectTimeout = WIFI_CONNECT_TIMEOUT;
}

void WiFiManager::init() {
    WiFi.mode(WIFI_STA);
    WiFi.setAutoConnect(false);
    WiFi.setAutoReconnect(false);
    
    Serial.println("WiFi Manager initialized");
}

void WiFiManager::update() {
    unsigned long now = millis();
    
    switch(currentStatus) {
        case WIFI_CONNECTING:
            // 检查连接超时
            if(now - connectStartTime > config.connectTimeout) {
                Serial.println("WiFi connection timeout");
                currentStatus = WIFI_FAILED;
                reconnectAttempts++;
                
                if(reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
                    Serial.println("Max reconnect attempts reached, starting AP mode");
                    startAPMode();
                }
            }
            // 检查连接状态
            else if(WiFi.status() == WL_CONNECTED) {
                currentStatus = WIFI_CONNECTED;
                reconnectAttempts = 0;
                Serial.print("WiFi connected! IP: ");
                Serial.println(WiFi.localIP());
            }
            break;
            
        case WIFI_FAILED:
            // 自动重连
            if(config.autoConnect && !apModeActive && 
               (now - lastConnectAttempt > WIFI_RECONNECT_INTERVAL)) {
                Serial.println("Attempting WiFi reconnection...");
                attemptConnection();
            }
            break;
            
        case WIFI_CONNECTED:
            // 检查连接是否断开
            if(WiFi.status() != WL_CONNECTED) {
                Serial.println("WiFi connection lost");
                currentStatus = WIFI_DISCONNECTED;
            }
            break;
            
        case WIFI_DISCONNECTED:
            // 自动连接
            if(config.autoConnect && strlen(config.ssid) > 0 && !apModeActive) {
                attemptConnection();
            }
            break;
            
        case WIFI_AP_MODE:
            // AP模式下的处理
            break;
    }
}

void WiFiManager::attemptConnection() {
    if(strlen(config.ssid) == 0) {
        Serial.println("No WiFi SSID configured");
        return;
    }
    
    Serial.print("Connecting to WiFi: ");
    Serial.println(config.ssid);
    
    WiFi.begin(config.ssid, config.password);
    currentStatus = WIFI_CONNECTING;
    connectStartTime = millis();
    lastConnectAttempt = connectStartTime;
}

void WiFiManager::startAPMode() {
    if(apModeActive) return;
    
    WiFi.mode(WIFI_AP);
    WiFi.softAP(AP_SSID, AP_PASSWORD);
    
    apModeActive = true;
    currentStatus = WIFI_AP_MODE;
    
    Serial.println("AP Mode started");
    Serial.print("AP IP: ");
    Serial.println(WiFi.softAPIP());
}

void WiFiManager::stopAPMode() {
    if(!apModeActive) return;
    
    WiFi.softAPdisconnect(true);
    WiFi.mode(WIFI_STA);
    
    apModeActive = false;
    currentStatus = WIFI_DISCONNECTED;
    
    Serial.println("AP Mode stopped");
}

void WiFiManager::setConfig(const char* ssid, const char* password, bool autoConnect) {
    strncpy(config.ssid, ssid, sizeof(config.ssid) - 1);
    strncpy(config.password, password, sizeof(config.password) - 1);
    config.autoConnect = autoConnect;
    
    Serial.print("WiFi config updated: ");
    Serial.println(config.ssid);
}

void WiFiManager::connect() {
    if(apModeActive) {
        stopAPMode();
    }
    
    reconnectAttempts = 0;
    attemptConnection();
}

void WiFiManager::disconnect() {
    WiFi.disconnect();
    currentStatus = WIFI_DISCONNECTED;
    Serial.println("WiFi disconnected");
}

WiFiStatus WiFiManager::getStatus() const {
    return currentStatus;
}

const WiFiConfig& WiFiManager::getConfig() const {
    return config;
}

bool WiFiManager::isConnected() const {
    return currentStatus == WIFI_CONNECTED && WiFi.status() == WL_CONNECTED;
}

String WiFiManager::getIPAddress() const {
    if(isConnected()) {
        return WiFi.localIP().toString();
    }
    return "0.0.0.0";
}

int32_t WiFiManager::getRSSI() const {
    if(isConnected()) {
        return WiFi.RSSI();
    }
    return 0;
}

void WiFiManager::startConfigMode() {
    startAPMode();
}

void WiFiManager::stopConfigMode() {
    stopAPMode();
}

bool WiFiManager::isInConfigMode() const {
    return apModeActive;
}

int WiFiManager::scanNetworks() {
    return WiFi.scanNetworks();
}

String WiFiManager::getScannedSSID(int index) const {
    return WiFi.SSID(index);
}

int32_t WiFiManager::getScannedRSSI(int index) const {
    return WiFi.RSSI(index);
}

bool WiFiManager::isScannedNetworkSecure(int index) const {
    return WiFi.encryptionType(index) != WIFI_AUTH_OPEN;
}
