/**
 * VFD Clock - 精简版本
 * 核心功能：RTC时间显示 + VFD控制 + 按键
 */

#include <Arduino.h>
#include <Wire.h>

// 硬件引脚定义
#define RTC_SDA_PIN 18
#define RTC_SCL_PIN 19
#define RTC_ADDRESS 0x32
#define VFD_CLK_PIN 3
#define VFD_DIN_PIN 5
#define VFD_CS_PIN  8
#define BUTTON1_PIN 9
#define BUTTON2_PIN 2
#define BUTTON3_PIN 10
#define BEEP_PIN    4

// 时间数据
struct {
    uint8_t hour, minute, second;
    uint8_t date, month, year;
} time;

// 显示状态
uint8_t displayMode = 0;  // 0=时间, 1=日期
uint8_t brightness = 2;
const uint8_t brightLevels[] = {0x03, 0x07, 0x0B, 0x0F};

// 工具函数
uint8_t bcdToDec(uint8_t val) { return ((val >> 4) * 10) + (val & 0x0F); }

// VFD控制
void vfdDelay() { for(volatile int i = 0; i < 1000; i++) __asm__("nop"); }

void vfdWrite(uint8_t data) {
    for(uint8_t i = 0; i < 8; i++) {
        digitalWrite(VFD_CLK_PIN, LOW);
        vfdDelay();
        digitalWrite(VFD_DIN_PIN, (data & 0x01) ? HIGH : LOW);
        data >>= 1;
        vfdDelay();
        digitalWrite(VFD_CLK_PIN, HIGH);
    }
}

void vfdCmd(uint8_t cmd) {
    digitalWrite(VFD_CS_PIN, LOW);
    vfdDelay();
    vfdWrite(cmd);
    digitalWrite(VFD_CS_PIN, HIGH);
    vfdDelay();
}

void vfdDisplay(const char* str) {
    digitalWrite(VFD_CS_PIN, LOW);
    vfdDelay();
    vfdWrite(0x20);
    while(*str) vfdWrite(*str++);
    digitalWrite(VFD_CS_PIN, HIGH);
    vfdCmd(0xE8);
}

void vfdInit() {
    vfdCmd(0xE0);
    vfdCmd(0x0F);
    vfdCmd(0xE4);
    vfdCmd(brightLevels[brightness]);
}

// RTC控制
void rtcInit() {
    Wire.begin(RTC_SDA_PIN, RTC_SCL_PIN);
    Wire.setClock(400000);
    Wire.beginTransmission(RTC_ADDRESS);
    Wire.write(0x0E);
    Wire.write(0x20);
    Wire.endTransmission();
}

void rtcRead() {
    Wire.beginTransmission(RTC_ADDRESS);
    Wire.write(0x00);
    Wire.endTransmission();
    Wire.requestFrom(RTC_ADDRESS, 7);
    
    if(Wire.available() >= 7) {
        time.second = bcdToDec(Wire.read());
        time.minute = bcdToDec(Wire.read());
        time.hour = bcdToDec(Wire.read());
        Wire.read(); // skip day of week
        time.date = bcdToDec(Wire.read());
        time.month = bcdToDec(Wire.read());
        time.year = bcdToDec(Wire.read());
    }
}

// 显示更新
void updateDisplay() {
    char buf[17];
    if(displayMode == 0) {
        sprintf(buf, "   %02d:%02d:%02d   ", time.hour, time.minute, time.second);
    } else {
        sprintf(buf, "%02d:%02d   %02d/%02d  ", time.hour, time.minute, time.date, time.month);
    }
    vfdDisplay(buf);
}

// 按键处理
void checkButtons() {
    static unsigned long lastPress[3] = {0};
    unsigned long now = millis();
    
    if(digitalRead(BUTTON1_PIN) == LOW && now - lastPress[0] > 300) {
        lastPress[0] = now;
        brightness = (brightness + 1) % 4;
        vfdCmd(0xE4);
        vfdCmd(brightLevels[brightness]);
    }
    
    if(digitalRead(BUTTON2_PIN) == LOW && now - lastPress[1] > 300) {
        lastPress[1] = now;
        displayMode = 1 - displayMode;
    }
}

void setup() {
    Serial.begin(115200);
    
    // 初始化引脚
    pinMode(BEEP_PIN, OUTPUT);
    digitalWrite(BEEP_PIN, HIGH);
    pinMode(VFD_CLK_PIN, OUTPUT);
    pinMode(VFD_DIN_PIN, OUTPUT);
    pinMode(VFD_CS_PIN, OUTPUT);
    pinMode(BUTTON1_PIN, INPUT_PULLUP);
    pinMode(BUTTON2_PIN, INPUT_PULLUP);
    pinMode(BUTTON3_PIN, INPUT_PULLUP);
    
    // 初始化硬件
    rtcInit();
    vfdInit();
    
    // 启动显示
    vfdDisplay(" VFD CLOCK V16  ");
    delay(2000);
}

void loop() {
    static unsigned long lastUpdate = 0;
    unsigned long now = millis();
    
    if(now - lastUpdate >= 1000) {
        lastUpdate = now;
        rtcRead();
        updateDisplay();
    }
    
    checkButtons();
    delay(50);
}
