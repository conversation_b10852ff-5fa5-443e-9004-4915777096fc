/**
 * VFD Clock - Main Application
 * 主程序文件 - 模块化重构版本
 *
 * 功能模块：
 * - VFD显示驱动
 * - RTC时间控制
 * - 时钟显示管理
 * - 按键处理
 */

#include <Arduino.h>
#include "config.h"
#include "vfd_driver.h"
#include "rtc.h"
#include "clock_display.h"
#include "buttons.h"

// 全局对象
VFDDriver vfdDriver;
RTCController rtcController;
ClockDisplay clockDisplay(&vfdDriver, &rtcController);
ButtonHandler buttonHandler(&vfdDriver, &clockDisplay);

void setup() {
    Serial.begin(115200);
    Serial.println("VFD Clock V16 - Starting...");

    // 初始化蜂鸣器引脚
    pinMode(BEEP_PIN, OUTPUT);
    digitalWrite(BEEP_PIN, HIGH);

    // 初始化各个模块
    if(!rtcController.init()) {
        Serial.println("RTC initialization failed!");
    } else {
        Serial.println("RTC initialized successfully");
    }

    clockDisplay.init();
    buttonHandler.init();

    Serial.println("VFD Clock V16 - Ready!");
    delay(2000);
}

void loop() {
    // 更新时钟显示
    clockDisplay.update();

    // 检查按键
    buttonHandler.check();

    // 短暂延时
    delay(50);
}
