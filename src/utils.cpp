/**
 * VFD Clock - Utility Functions Implementation
 * 工具函数实现
 */

#include "utils.h"
#include "config.h"
#include <stdio.h>

uint8_t bcdToDec(uint8_t val) {
    return ((val >> 4) * 10) + (val & 0x0F);
}

uint8_t decToBcd(uint8_t val) {
    return ((val / 10) << 4) + (val % 10);
}

void formatTimeString(char* buf, uint8_t hour, uint8_t minute, uint8_t second) {
    sprintf(buf, "   %02d:%02d:%02d   ", hour, minute, second);
}

void formatDateString(char* buf, uint8_t hour, uint8_t minute, uint8_t date, uint8_t month) {
    sprintf(buf, "%02d:%02d   %02d/%02d  ", hour, minute, date, month);
}
