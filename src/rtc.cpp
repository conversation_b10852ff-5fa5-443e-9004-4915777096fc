/**
 * VFD Clock - RTC Implementation
 * 实时时钟实现
 */

#include "rtc.h"
#include "config.h"
#include "utils.h"
#include <Wire.h>

RTCController::RTCController() {
    // 初始化时间数据
    memset(&currentTime, 0, sizeof(TimeData));
}

bool RTCController::init() {
    Wire.begin(RTC_SDA_PIN, RTC_SCL_PIN);
    Wire.setClock(400000);
    
    // 配置RTC
    Wire.beginTransmission(RTC_ADDRESS);
    Wire.write(0x0E);  // 控制寄存器
    Wire.write(0x20);  // 启用32kHz输出
    uint8_t error = Wire.endTransmission();
    
    return (error == 0);
}

bool RTCController::readTime() {
    Wire.beginTransmission(RTC_ADDRESS);
    Wire.write(0x00);  // 从秒寄存器开始读取
    uint8_t error = Wire.endTransmission();
    
    if(error != 0) {
        return false;
    }
    
    Wire.requestFrom(RTC_ADDRESS, 7);
    
    if(Wire.available() >= 7) {
        currentTime.second = bcdToDec(Wire.read());
        currentTime.minute = bcdToDec(Wire.read());
        currentTime.hour = bcdToDec(Wire.read());
        currentTime.dayOfWeek = bcdToDec(Wire.read());
        currentTime.date = bcdToDec(Wire.read());
        currentTime.month = bcdToDec(Wire.read());
        currentTime.year = bcdToDec(Wire.read());
        return true;
    }
    
    return false;
}

bool RTCController::setTime(const TimeData& time) {
    Wire.beginTransmission(RTC_ADDRESS);
    Wire.write(0x00);  // 从秒寄存器开始写入
    Wire.write(decToBcd(time.second));
    Wire.write(decToBcd(time.minute));
    Wire.write(decToBcd(time.hour));
    Wire.write(decToBcd(time.dayOfWeek));
    Wire.write(decToBcd(time.date));
    Wire.write(decToBcd(time.month));
    Wire.write(decToBcd(time.year));
    uint8_t error = Wire.endTransmission();
    
    if(error == 0) {
        currentTime = time;
        return true;
    }
    
    return false;
}

const TimeData& RTCController::getTime() const {
    return currentTime;
}

bool RTCController::isRunning() {
    // 读取状态寄存器检查振荡器是否停止
    Wire.beginTransmission(RTC_ADDRESS);
    Wire.write(0x0F);  // 状态寄存器
    uint8_t error = Wire.endTransmission();
    
    if(error != 0) {
        return false;
    }
    
    Wire.requestFrom(RTC_ADDRESS, 1);
    if(Wire.available()) {
        uint8_t status = Wire.read();
        return !(status & 0x80);  // OSF位为0表示振荡器正常
    }
    
    return false;
}
