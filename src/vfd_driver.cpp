/**
 * VFD Clock - VFD Driver Implementation
 * VFD显示驱动实现
 */

#include "vfd_driver.h"
#include "config.h"

// 亮度等级定义
const uint8_t brightLevels[BRIGHTNESS_LEVELS] = {0x03, 0x07, 0x0B, 0x0F};

VFDDriver::VFDDriver() : brightness(2) {
    // 初始化引脚
    pinMode(VFD_CLK_PIN, OUTPUT);
    pinMode(VFD_DIN_PIN, OUTPUT);
    pinMode(VFD_CS_PIN, OUTPUT);
}

void VFDDriver::vfdDelay() {
    for(volatile int i = 0; i < 1000; i++) {
        __asm__("nop");
    }
}

void VFDDriver::vfdWrite(uint8_t data) {
    for(uint8_t i = 0; i < 8; i++) {
        digitalWrite(VFD_CLK_PIN, LOW);
        vfdDelay();
        digitalWrite(VFD_DIN_PIN, (data & 0x01) ? HIGH : LOW);
        data >>= 1;
        vfdDelay();
        digitalWrite(VFD_CLK_PIN, HIGH);
    }
}

void VFDDriver::vfdCmd(uint8_t cmd) {
    digitalWrite(VFD_CS_PIN, LOW);
    vfdDelay();
    vfdWrite(cmd);
    digitalWrite(VFD_CS_PIN, HIGH);
    vfdDelay();
}

void VFDDriver::init() {
    vfdCmd(0xE0);
    vfdCmd(0x0F);
    vfdCmd(0xE4);
    vfdCmd(brightLevels[brightness]);
}

void VFDDriver::display(const char* str) {
    digitalWrite(VFD_CS_PIN, LOW);
    vfdDelay();
    vfdWrite(0x20);
    while(*str) {
        vfdWrite(*str++);
    }
    digitalWrite(VFD_CS_PIN, HIGH);
    vfdCmd(0xE8);
}

void VFDDriver::setBrightness(uint8_t level) {
    if(level < BRIGHTNESS_LEVELS) {
        brightness = level;
        vfdCmd(0xE4);
        vfdCmd(brightLevels[brightness]);
    }
}

uint8_t VFDDriver::getBrightness() const {
    return brightness;
}

void VFDDriver::clear() {
    display("                ");
}
