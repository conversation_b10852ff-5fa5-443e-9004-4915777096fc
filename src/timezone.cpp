/**
 * VFD Clock - Timezone Manager Implementation
 * 时区管理和夏令时处理实现
 */

#include "timezone.h"
#include "config.h"
#include <time.h>

// 预定义时区名称
static const char* timezoneNames[] = {
    "UTC",
    "Asia/Shanghai",
    "Asia/Tokyo", 
    "America/New_York",
    "America/Los_Angeles",
    "Europe/London",
    "Europe/Paris",
    "Australia/Sydney",
    "Custom"
};

TimezoneManager::TimezoneManager() : isDSTActive(false) {
    // 默认设置为UTC
    setTimezone(TZ_UTC);
}

void TimezoneManager::init() {
    updateDSTStatus();
    Serial.println("Timezone Manager initialized");
}

void TimezoneManager::loadPredefinedTimezone(PredefinedTimezone tz) {
    switch(tz) {
        case TZ_UTC:
            strcpy(currentTimezone.name, "UTC");
            strcpy(currentTimezone.abbreviation, "UTC");
            currentTimezone.offsetMinutes = 0;
            currentTimezone.dstEnabled = false;
            break;
            
        case TZ_BEIJING:
            strcpy(currentTimezone.name, "Asia/Shanghai");
            strcpy(currentTimezone.abbreviation, "CST");
            currentTimezone.offsetMinutes = 8 * 60;  // UTC+8
            currentTimezone.dstEnabled = false;
            break;
            
        case TZ_TOKYO:
            strcpy(currentTimezone.name, "Asia/Tokyo");
            strcpy(currentTimezone.abbreviation, "JST");
            currentTimezone.offsetMinutes = 9 * 60;  // UTC+9
            currentTimezone.dstEnabled = false;
            break;
            
        case TZ_NEW_YORK:
            strcpy(currentTimezone.name, "America/New_York");
            strcpy(currentTimezone.abbreviation, "EST");
            currentTimezone.offsetMinutes = -5 * 60;  // UTC-5
            currentTimezone.dstEnabled = true;
            // 夏令时开始：3月第二个周日 2:00 AM
            currentTimezone.dstStart = {3, 2, 0, 2, 60};
            // 夏令时结束：11月第一个周日 2:00 AM
            currentTimezone.dstEnd = {11, 1, 0, 2, 0};
            currentTimezone.dstOffset = 60;
            break;
            
        case TZ_LOS_ANGELES:
            strcpy(currentTimezone.name, "America/Los_Angeles");
            strcpy(currentTimezone.abbreviation, "PST");
            currentTimezone.offsetMinutes = -8 * 60;  // UTC-8
            currentTimezone.dstEnabled = true;
            // 夏令时规则同纽约
            currentTimezone.dstStart = {3, 2, 0, 2, 60};
            currentTimezone.dstEnd = {11, 1, 0, 2, 0};
            currentTimezone.dstOffset = 60;
            break;
            
        case TZ_LONDON:
            strcpy(currentTimezone.name, "Europe/London");
            strcpy(currentTimezone.abbreviation, "GMT");
            currentTimezone.offsetMinutes = 0;  // UTC+0
            currentTimezone.dstEnabled = true;
            // 夏令时开始：3月最后一个周日 1:00 AM
            currentTimezone.dstStart = {3, 5, 0, 1, 60};
            // 夏令时结束：10月最后一个周日 2:00 AM
            currentTimezone.dstEnd = {10, 5, 0, 2, 0};
            currentTimezone.dstOffset = 60;
            break;
            
        case TZ_PARIS:
            strcpy(currentTimezone.name, "Europe/Paris");
            strcpy(currentTimezone.abbreviation, "CET");
            currentTimezone.offsetMinutes = 1 * 60;  // UTC+1
            currentTimezone.dstEnabled = true;
            // 夏令时规则同伦敦
            currentTimezone.dstStart = {3, 5, 0, 1, 60};
            currentTimezone.dstEnd = {10, 5, 0, 2, 0};
            currentTimezone.dstOffset = 60;
            break;
            
        case TZ_SYDNEY:
            strcpy(currentTimezone.name, "Australia/Sydney");
            strcpy(currentTimezone.abbreviation, "AEST");
            currentTimezone.offsetMinutes = 10 * 60;  // UTC+10
            currentTimezone.dstEnabled = true;
            // 夏令时开始：10月第一个周日 2:00 AM
            currentTimezone.dstStart = {10, 1, 0, 2, 60};
            // 夏令时结束：4月第一个周日 3:00 AM
            currentTimezone.dstEnd = {4, 1, 0, 3, 0};
            currentTimezone.dstOffset = 60;
            break;
            
        default:
            loadPredefinedTimezone(TZ_UTC);
            break;
    }
}

void TimezoneManager::setTimezone(PredefinedTimezone tz) {
    loadPredefinedTimezone(tz);
    updateDSTStatus();

    Serial.print("Timezone set to: ");
    Serial.println(currentTimezone.name);
}

void TimezoneManager::setCustomTimezone(const char* name, int16_t offsetMinutes, bool dstEnabled) {
    strcpy(currentTimezone.name, name);
    strcpy(currentTimezone.abbreviation, "CUSTOM");
    currentTimezone.offsetMinutes = offsetMinutes;
    currentTimezone.dstEnabled = dstEnabled;

    if(!dstEnabled) {
        isDSTActive = false;
    }

    Serial.print("Custom timezone set: ");
    Serial.print(name);
    Serial.print(" (UTC");
    if(offsetMinutes >= 0) Serial.print("+");
    Serial.print(offsetMinutes / 60.0, 1);
    Serial.println(")");
}

void TimezoneManager::setDSTRules(const DSTRule& startRule, const DSTRule& endRule, int16_t dstOffset) {
    currentTimezone.dstStart = startRule;
    currentTimezone.dstEnd = endRule;
    currentTimezone.dstOffset = dstOffset;
    currentTimezone.dstEnabled = true;

    updateDSTStatus();
}

TimeData TimezoneManager::utcToLocal(const TimeData& utcTime) {
    // 将TimeData转换为time_t
    struct tm timeStruct = {0};
    timeStruct.tm_year = utcTime.year + 100;  // tm_year从1900年开始
    timeStruct.tm_mon = utcTime.month - 1;    // tm_mon从0开始
    timeStruct.tm_mday = utcTime.date;
    timeStruct.tm_hour = utcTime.hour;
    timeStruct.tm_min = utcTime.minute;
    timeStruct.tm_sec = utcTime.second;

    time_t utcTimestamp = mktime(&timeStruct);

    // 应用时区偏移
    int16_t totalOffset = currentTimezone.offsetMinutes;
    if(currentTimezone.dstEnabled && isDSTTime(utcTimestamp)) {
        totalOffset += currentTimezone.dstOffset;
    }

    time_t localTimestamp = utcTimestamp + (totalOffset * 60);

    // 转换回TimeData
    struct tm* localTm = localtime(&localTimestamp);
    TimeData localTime;
    localTime.year = localTm->tm_year - 100;
    localTime.month = localTm->tm_mon + 1;
    localTime.date = localTm->tm_mday;
    localTime.hour = localTm->tm_hour;
    localTime.minute = localTm->tm_min;
    localTime.second = localTm->tm_sec;
    localTime.dayOfWeek = localTm->tm_wday;

    return localTime;
}

TimeData TimezoneManager::localToUtc(const TimeData& localTime) {
    // 将TimeData转换为time_t
    struct tm timeStruct = {0};
    timeStruct.tm_year = localTime.year + 100;
    timeStruct.tm_mon = localTime.month - 1;
    timeStruct.tm_mday = localTime.date;
    timeStruct.tm_hour = localTime.hour;
    timeStruct.tm_min = localTime.minute;
    timeStruct.tm_sec = localTime.second;

    time_t localTimestamp = mktime(&timeStruct);

    // 移除时区偏移
    int16_t totalOffset = currentTimezone.offsetMinutes;
    if(currentTimezone.dstEnabled && isDSTTime(localTimestamp)) {
        totalOffset += currentTimezone.dstOffset;
    }

    time_t utcTimestamp = localTimestamp - (totalOffset * 60);

    // 转换回TimeData
    struct tm* utcTm = gmtime(&utcTimestamp);
    TimeData utcTime;
    utcTime.year = utcTm->tm_year - 100;
    utcTime.month = utcTm->tm_mon + 1;
    utcTime.date = utcTm->tm_mday;
    utcTime.hour = utcTm->tm_hour;
    utcTime.minute = utcTm->tm_min;
    utcTime.second = utcTm->tm_sec;
    utcTime.dayOfWeek = utcTm->tm_wday;

    return utcTime;
}

time_t TimezoneManager::calculateDSTDate(int year, const DSTRule& rule) {
    struct tm timeStruct = {0};
    timeStruct.tm_year = year - 1900;
    timeStruct.tm_mon = rule.month - 1;
    timeStruct.tm_mday = 1;
    timeStruct.tm_hour = rule.hour;
    timeStruct.tm_min = 0;
    timeStruct.tm_sec = 0;

    time_t firstOfMonth = mktime(&timeStruct);
    struct tm* firstTm = localtime(&firstOfMonth);

    int firstWeekday = firstTm->tm_wday;
    int targetDay;

    if(rule.week == 5) {  // 最后一周
        // 找到月末
        timeStruct.tm_mon++;
        if(timeStruct.tm_mon == 12) {
            timeStruct.tm_year++;
            timeStruct.tm_mon = 0;
        }
        timeStruct.tm_mday = 0;  // 上个月的最后一天
        time_t lastOfMonth = mktime(&timeStruct);
        struct tm* lastTm = localtime(&lastOfMonth);

        int lastDay = lastTm->tm_mday;
        int lastWeekday = lastTm->tm_wday;

        // 计算最后一个指定星期几
        int daysBack = (lastWeekday - rule.dayOfWeek + 7) % 7;
        targetDay = lastDay - daysBack;
    } else {
        // 计算第N个指定星期几
        int daysToAdd = (rule.dayOfWeek - firstWeekday + 7) % 7;
        targetDay = 1 + daysToAdd + (rule.week - 1) * 7;
    }

    timeStruct.tm_year = year - 1900;
    timeStruct.tm_mon = rule.month - 1;
    timeStruct.tm_mday = targetDay;
    timeStruct.tm_hour = rule.hour;
    timeStruct.tm_min = 0;
    timeStruct.tm_sec = 0;

    return mktime(&timeStruct);
}

bool TimezoneManager::isDSTTime(time_t timestamp) {
    if(!currentTimezone.dstEnabled) {
        return false;
    }

    struct tm* timeTm = localtime(&timestamp);
    int year = timeTm->tm_year + 1900;

    time_t dstStart = calculateDSTDate(year, currentTimezone.dstStart);
    time_t dstEnd = calculateDSTDate(year, currentTimezone.dstEnd);

    // 处理南半球情况（夏令时跨年）
    if(dstStart > dstEnd) {
        return timestamp >= dstStart || timestamp < dstEnd;
    } else {
        return timestamp >= dstStart && timestamp < dstEnd;
    }
}

const TimezoneConfig& TimezoneManager::getTimezoneConfig() const {
    return currentTimezone;
}

int16_t TimezoneManager::getCurrentOffset() {
    int16_t offset = currentTimezone.offsetMinutes;
    if(currentTimezone.dstEnabled && isDSTActive) {
        offset += currentTimezone.dstOffset;
    }
    return offset;
}

bool TimezoneManager::isDSTActive() const {
    return isDSTActive;
}

const char* TimezoneManager::getTimezoneName() const {
    return currentTimezone.name;
}

const char* TimezoneManager::getTimezoneAbbreviation() const {
    return currentTimezone.abbreviation;
}

void TimezoneManager::updateDSTStatus() {
    if(!currentTimezone.dstEnabled) {
        isDSTActive = false;
        return;
    }

    time_t now = time(nullptr);
    isDSTActive = isDSTTime(now);
}

void TimezoneManager::formatTimezoneInfo(char* buffer, size_t size) {
    int16_t offset = getCurrentOffset();
    int hours = abs(offset) / 60;
    int minutes = abs(offset) % 60;

    snprintf(buffer, size, "%s (UTC%c%02d:%02d)%s",
             currentTimezone.abbreviation,
             offset >= 0 ? '+' : '-',
             hours, minutes,
             isDSTActive ? " DST" : "");
}

const char* TimezoneManager::getAvailableTimezone(int index) {
    if(index >= 0 && index < sizeof(timezoneNames) / sizeof(timezoneNames[0])) {
        return timezoneNames[index];
    }
    return nullptr;
}

int TimezoneManager::getAvailableTimezoneCount() {
    return sizeof(timezoneNames) / sizeof(timezoneNames[0]);
}
