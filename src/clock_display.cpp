/**
 * VFD Clock - Clock Display Implementation
 * 时钟显示实现
 */

#include "clock_display.h"
#include "config.h"
#include "utils.h"

ClockDisplay::ClockDisplay(VFDDriver* vfdDriver, RTCController* rtcController) 
    : vfd(vfdDriver), rtc(rtcController), currentMode(MODE_TIME), lastUpdate(0) {
}

void ClockDisplay::init() {
    if(vfd) {
        vfd->init();
    }
    showStartupMessage();
}

void ClockDisplay::update(bool forceUpdate) {
    if(!vfd || !rtc) {
        return;
    }
    
    unsigned long now = millis();
    
    // 检查是否需要更新
    if(!forceUpdate && (now - lastUpdate < UPDATE_INTERVAL)) {
        return;
    }
    
    // 读取时间
    if(!rtc->readTime()) {
        vfd->display("  RTC ERROR!   ");
        return;
    }
    
    const TimeData& time = rtc->getTime();
    char buffer[DISPLAY_WIDTH + 1];
    
    // 根据显示模式格式化字符串
    switch(currentMode) {
        case MODE_TIME:
            formatTimeString(buffer, time.hour, time.minute, time.second);
            break;
            
        case MODE_DATE:
            formatDateString(buffer, time.hour, time.minute, time.date, time.month);
            break;
            
        default:
            formatTimeString(buffer, time.hour, time.minute, time.second);
            break;
    }
    
    vfd->display(buffer);
    lastUpdate = now;
}

void ClockDisplay::toggleMode() {
    currentMode = (currentMode == MODE_TIME) ? MODE_DATE : MODE_TIME;
    update(true);  // 强制更新显示
}

void ClockDisplay::setMode(DisplayMode mode) {
    if(currentMode != mode) {
        currentMode = mode;
        update(true);  // 强制更新显示
    }
}

DisplayMode ClockDisplay::getMode() const {
    return currentMode;
}

void ClockDisplay::showStartupMessage() {
    if(vfd) {
        vfd->display(" VFD CLOCK V16  ");
    }
}

void ClockDisplay::showMessage(const char* message, unsigned long duration) {
    if(vfd) {
        vfd->display(message);
        delay(duration);
        update(true);  // 恢复正常显示
    }
}
