/**
 * VFD Clock - Settings Manager Implementation
 * 设置管理和存储实现
 */

#include "settings.h"
#include "config.h"
#include <ArduinoJson.h>

#define SETTINGS_NAMESPACE "vfd_clock"
#define SETTINGS_VERSION 1
#define AUTO_SAVE_INTERVAL 30000  // 30秒自动保存

SettingsManager::SettingsManager() 
    : settingsLoaded(false), settingsChanged(false), lastSaveTime(0) {
}

SettingsManager::~SettingsManager() {
    if(settingsChanged) {
        saveSettings(true);
    }
    preferences.end();
}

bool SettingsManager::init() {
    if(!preferences.begin(SETTINGS_NAMESPACE, false)) {
        Serial.println("Failed to initialize preferences");
        return false;
    }
    
    loadDefaults();
    
    if(!loadSettings()) {
        Serial.println("Failed to load settings, using defaults");
        saveSettings(true);
    }
    
    Serial.println("Settings Manager initialized");
    return true;
}

void SettingsManager::loadDefaults() {
    // 显示设置
    currentSettings.brightness = 2;
    currentSettings.displayMode = 0;
    currentSettings.autoDisplayMode = false;
    currentSettings.displayTimeout = 0;
    
    // WiFi设置
    memset(&currentSettings.wifiConfig, 0, sizeof(WiFiConfig));
    currentSettings.wifiConfig.autoConnect = true;
    currentSettings.wifiConfig.connectTimeout = 30000;
    
    // NTP设置
    strcpy(currentSettings.ntpServer1, "pool.ntp.org");
    strcpy(currentSettings.ntpServer2, "time.nist.gov");
    strcpy(currentSettings.ntpServer3, "time.google.com");
    currentSettings.ntpSyncInterval = 3600;  // 1小时
    currentSettings.autoTimeSync = true;
    
    // 时区设置
    currentSettings.timezone = TZ_UTC;
    memset(&currentSettings.customTimezone, 0, sizeof(TimezoneConfig));
    
    // 系统设置
    currentSettings.serialDebug = true;
    currentSettings.webServerPort = 80;
    strcpy(currentSettings.deviceName, "VFD-Clock");
    
    // 版本信息
    currentSettings.settingsVersion = SETTINGS_VERSION;
    currentSettings.lastModified = 0;
    
    settingsLoaded = true;
}

bool SettingsManager::loadSettings() {
    if(!preferences.isKey("version")) {
        return false;  // 没有保存的设置
    }
    
    uint16_t version = preferences.getUShort("version", 0);
    if(version != SETTINGS_VERSION) {
        migrateSettings(version);
    }
    
    // 加载显示设置
    currentSettings.brightness = preferences.getUChar("brightness", currentSettings.brightness);
    currentSettings.displayMode = preferences.getUChar("displayMode", currentSettings.displayMode);
    currentSettings.autoDisplayMode = preferences.getBool("autoDisplay", currentSettings.autoDisplayMode);
    
    // 加载WiFi设置
    preferences.getString("wifiSSID", currentSettings.wifiConfig.ssid, sizeof(currentSettings.wifiConfig.ssid));
    preferences.getString("wifiPass", currentSettings.wifiConfig.password, sizeof(currentSettings.wifiConfig.password));
    currentSettings.wifiConfig.autoConnect = preferences.getBool("wifiAuto", currentSettings.wifiConfig.autoConnect);
    
    // 加载NTP设置
    preferences.getString("ntpServer1", currentSettings.ntpServer1, sizeof(currentSettings.ntpServer1));
    preferences.getString("ntpServer2", currentSettings.ntpServer2, sizeof(currentSettings.ntpServer2));
    preferences.getString("ntpServer3", currentSettings.ntpServer3, sizeof(currentSettings.ntpServer3));
    currentSettings.ntpSyncInterval = preferences.getULong("ntpInterval", currentSettings.ntpSyncInterval);
    currentSettings.autoTimeSync = preferences.getBool("autoSync", currentSettings.autoTimeSync);
    
    // 加载时区设置
    currentSettings.timezone = (PredefinedTimezone)preferences.getUChar("timezone", currentSettings.timezone);
    
    // 加载系统设置
    currentSettings.serialDebug = preferences.getBool("serialDebug", currentSettings.serialDebug);
    currentSettings.webServerPort = preferences.getUShort("webPort", currentSettings.webServerPort);
    preferences.getString("deviceName", currentSettings.deviceName, sizeof(currentSettings.deviceName));
    
    currentSettings.lastModified = preferences.getULong("lastModified", 0);
    
    if(!validateSettings()) {
        Serial.println("Settings validation failed, using defaults");
        loadDefaults();
        return false;
    }
    
    settingsLoaded = true;
    settingsChanged = false;
    
    Serial.println("Settings loaded successfully");
    return true;
}

bool SettingsManager::saveSettings(bool force) {
    if(!settingsChanged && !force) {
        return true;
    }
    
    currentSettings.settingsVersion = SETTINGS_VERSION;
    currentSettings.lastModified = millis();
    
    // 保存版本信息
    preferences.putUShort("version", currentSettings.settingsVersion);
    
    // 保存显示设置
    preferences.putUChar("brightness", currentSettings.brightness);
    preferences.putUChar("displayMode", currentSettings.displayMode);
    preferences.putBool("autoDisplay", currentSettings.autoDisplayMode);
    
    // 保存WiFi设置
    preferences.putString("wifiSSID", currentSettings.wifiConfig.ssid);
    preferences.putString("wifiPass", currentSettings.wifiConfig.password);
    preferences.putBool("wifiAuto", currentSettings.wifiConfig.autoConnect);
    
    // 保存NTP设置
    preferences.putString("ntpServer1", currentSettings.ntpServer1);
    preferences.putString("ntpServer2", currentSettings.ntpServer2);
    preferences.putString("ntpServer3", currentSettings.ntpServer3);
    preferences.putULong("ntpInterval", currentSettings.ntpSyncInterval);
    preferences.putBool("autoSync", currentSettings.autoTimeSync);
    
    // 保存时区设置
    preferences.putUChar("timezone", currentSettings.timezone);
    
    // 保存系统设置
    preferences.putBool("serialDebug", currentSettings.serialDebug);
    preferences.putUShort("webPort", currentSettings.webServerPort);
    preferences.putString("deviceName", currentSettings.deviceName);
    
    preferences.putULong("lastModified", currentSettings.lastModified);
    
    settingsChanged = false;
    lastSaveTime = millis();
    
    Serial.println("Settings saved successfully");
    return true;
}

bool SettingsManager::validateSettings() {
    // 验证亮度设置
    if(currentSettings.brightness >= BRIGHTNESS_LEVELS) {
        currentSettings.brightness = 2;
    }
    
    // 验证显示模式
    if(currentSettings.displayMode > 1) {
        currentSettings.displayMode = 0;
    }
    
    // 验证NTP同步间隔
    if(currentSettings.ntpSyncInterval < 60 || currentSettings.ntpSyncInterval > 86400) {
        currentSettings.ntpSyncInterval = 3600;
    }
    
    // 验证Web服务器端口
    if(currentSettings.webServerPort < 80 || currentSettings.webServerPort > 65535) {
        currentSettings.webServerPort = 80;
    }
    
    // 验证设备名称
    if(strlen(currentSettings.deviceName) == 0) {
        strcpy(currentSettings.deviceName, "VFD-Clock");
    }
    
    return true;
}

void SettingsManager::migrateSettings(uint16_t oldVersion) {
    Serial.print("Migrating settings from version ");
    Serial.print(oldVersion);
    Serial.print(" to ");
    Serial.println(SETTINGS_VERSION);
    
    // 这里可以添加版本迁移逻辑
    // 目前只有版本1，所以暂时不需要迁移
}

void SettingsManager::resetToDefaults() {
    loadDefaults();
    settingsChanged = true;
    saveSettings(true);
    Serial.println("Settings reset to defaults");
}

const SystemSettings& SettingsManager::getSettings() const {
    return currentSettings;
}

void SettingsManager::updateSettings(const SystemSettings& settings) {
    currentSettings = settings;
    settingsChanged = true;
}

void SettingsManager::setBrightness(uint8_t brightness) {
    if(brightness < BRIGHTNESS_LEVELS && currentSettings.brightness != brightness) {
        currentSettings.brightness = brightness;
        markChanged();
    }
}

uint8_t SettingsManager::getBrightness() const {
    return currentSettings.brightness;
}

void SettingsManager::setDisplayMode(uint8_t mode) {
    if(mode <= 1 && currentSettings.displayMode != mode) {
        currentSettings.displayMode = mode;
        markChanged();
    }
}

uint8_t SettingsManager::getDisplayMode() const {
    return currentSettings.displayMode;
}

void SettingsManager::setWiFiConfig(const WiFiConfig& config) {
    if(memcmp(&currentSettings.wifiConfig, &config, sizeof(WiFiConfig)) != 0) {
        currentSettings.wifiConfig = config;
        markChanged();
    }
}

const WiFiConfig& SettingsManager::getWiFiConfig() const {
    return currentSettings.wifiConfig;
}

void SettingsManager::markChanged() {
    settingsChanged = true;
}

void SettingsManager::update() {
    // 定期自动保存
    if(settingsChanged && (millis() - lastSaveTime > AUTO_SAVE_INTERVAL)) {
        saveSettings();
    }
}

bool SettingsManager::hasChanges() const {
    return settingsChanged;
}
