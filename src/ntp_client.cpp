/**
 * VFD Clock - NTP Client Implementation
 * NTP网络时间同步实现
 */

#include "ntp_client.h"
#include "config.h"

// NTP常量
#define NTP_PACKET_SIZE 48
#define NTP_DEFAULT_PORT 123
#define NTP_REQUEST_TIMEOUT 5000
#define NTP_MAX_RETRIES 3
#define NTP_EPOCH_OFFSET 2208988800UL  // 1900年到1970年的秒数

// 默认NTP服务器
#define DEFAULT_PRIMARY_SERVER "pool.ntp.org"
#define DEFAULT_SECONDARY_SERVER "time.nist.gov"
#define DEFAULT_TERTIARY_SERVER "time.google.com"

NTPClient::NTPClient() 
    : currentStatus(NTP_IDLE), lastSyncAttempt(0), requestStartTime(0),
      currentServerIndex(0), retryCount(0) {
    
    // 初始化配置
    strcpy(config.primaryServer, DEFAULT_PRIMARY_SERVER);
    strcpy(config.secondaryServer, DEFAULT_SECONDARY_SERVER);
    strcpy(config.tertiaryServer, DEFAULT_TERTIARY_SERVER);
    config.port = NTP_DEFAULT_PORT;
    config.syncInterval = 3600;  // 1小时
    config.requestTimeout = NTP_REQUEST_TIMEOUT;
    config.autoSync = true;
    
    // 初始化时间数据
    memset(&lastSyncTime, 0, sizeof(NTPTime));
}

void NTPClient::init() {
    udp.begin(config.port);
    Serial.println("NTP Client initialized");
}

void NTPClient::update() {
    unsigned long now = millis();
    
    switch(currentStatus) {
        case NTP_REQUESTING:
            // 检查是否有响应
            if(udp.parsePacket() >= NTP_PACKET_SIZE) {
                if(parseNTPResponse(lastSyncTime)) {
                    currentStatus = NTP_SUCCESS;
                    retryCount = 0;
                    Serial.println("NTP sync successful");
                } else {
                    currentStatus = NTP_FAILED;
                    Serial.println("NTP response parse failed");
                }
            }
            // 检查超时
            else if(now - requestStartTime > config.requestTimeout) {
                currentStatus = NTP_TIMEOUT;
                retryCount++;
                Serial.println("NTP request timeout");
                
                // 重试或切换服务器
                if(retryCount < NTP_MAX_RETRIES) {
                    switchToNextServer();
                    delay(1000);
                    sendNTPRequest(getCurrentServer());
                    requestStartTime = now;
                } else {
                    currentStatus = NTP_FAILED;
                    retryCount = 0;
                }
            }
            break;
            
        case NTP_IDLE:
            // 检查是否需要自动同步
            if(config.autoSync && needsSync()) {
                syncTime();
            }
            break;
            
        case NTP_SUCCESS:
        case NTP_FAILED:
        case NTP_TIMEOUT:
            // 重置状态
            currentStatus = NTP_IDLE;
            break;
    }
}

bool NTPClient::sendNTPRequest(const char* serverName) {
    if(!serverName || strlen(serverName) == 0) {
        return false;
    }
    
    // 创建NTP请求包
    uint8_t packetBuffer[NTP_PACKET_SIZE];
    memset(packetBuffer, 0, NTP_PACKET_SIZE);
    
    // 设置NTP请求头
    packetBuffer[0] = 0b11100011;   // LI, Version, Mode
    packetBuffer[1] = 0;            // Stratum
    packetBuffer[2] = 6;            // Polling Interval
    packetBuffer[3] = 0xEC;         // Peer Clock Precision
    
    // 发送请求
    udp.beginPacket(serverName, config.port);
    udp.write(packetBuffer, NTP_PACKET_SIZE);
    bool success = udp.endPacket();
    
    if(success) {
        Serial.print("NTP request sent to: ");
        Serial.println(serverName);
    }
    
    return success;
}

bool NTPClient::parseNTPResponse(NTPTime& ntpTime) {
    uint8_t packetBuffer[NTP_PACKET_SIZE];
    
    if(udp.read(packetBuffer, NTP_PACKET_SIZE) != NTP_PACKET_SIZE) {
        return false;
    }
    
    // 提取时间戳（字节40-43是传输时间戳）
    uint32_t timestamp = 0;
    for(int i = 0; i < 4; i++) {
        timestamp = (timestamp << 8) | packetBuffer[40 + i];
    }
    
    // 提取分数部分（字节44-47）
    uint32_t fraction = 0;
    for(int i = 0; i < 4; i++) {
        fraction = (fraction << 8) | packetBuffer[44 + i];
    }
    
    // 转换为Unix时间戳
    ntpTime.timestamp = timestamp - NTP_EPOCH_OFFSET;
    ntpTime.fraction = fraction;
    ntpTime.valid = true;
    ntpTime.receivedAt = millis();
    
    return true;
}

const char* NTPClient::getCurrentServer() {
    switch(currentServerIndex) {
        case 0: return config.primaryServer;
        case 1: return config.secondaryServer;
        case 2: return config.tertiaryServer;
        default: return config.primaryServer;
    }
}

void NTPClient::switchToNextServer() {
    currentServerIndex = (currentServerIndex + 1) % 3;
}

TimeData NTPClient::ntpToTimeData(const NTPTime& ntpTime) {
    TimeData timeData;
    
    // 计算当前时间（考虑接收延迟）
    uint32_t currentTimestamp = ntpTime.timestamp + 
                               (millis() - ntpTime.receivedAt) / 1000;
    
    // 转换为时间结构
    time_t rawTime = currentTimestamp;
    struct tm* timeInfo = gmtime(&rawTime);
    
    timeData.year = timeInfo->tm_year - 100;  // 从2000年开始计算
    timeData.month = timeInfo->tm_mon + 1;
    timeData.date = timeInfo->tm_mday;
    timeData.hour = timeInfo->tm_hour;
    timeData.minute = timeInfo->tm_min;
    timeData.second = timeInfo->tm_sec;
    timeData.dayOfWeek = timeInfo->tm_wday;
    
    return timeData;
}

void NTPClient::setConfig(const char* primary, const char* secondary, 
                         const char* tertiary, uint32_t syncInterval) {
    if(primary) strcpy(config.primaryServer, primary);
    if(secondary) strcpy(config.secondaryServer, secondary);
    if(tertiary) strcpy(config.tertiaryServer, tertiary);
    config.syncInterval = syncInterval;
}

bool NTPClient::syncTime() {
    if(currentStatus == NTP_REQUESTING) {
        return false;  // 已在请求中
    }
    
    currentStatus = NTP_REQUESTING;
    requestStartTime = millis();
    lastSyncAttempt = requestStartTime;
    currentServerIndex = 0;
    retryCount = 0;
    
    return sendNTPRequest(getCurrentServer());
}

void NTPClient::forceSyncTime() {
    currentStatus = NTP_IDLE;
    syncTime();
}

NTPStatus NTPClient::getStatus() const {
    return currentStatus;
}

const NTPTime& NTPClient::getLastSyncTime() const {
    return lastSyncTime;
}

bool NTPClient::needsSync() const {
    if(!lastSyncTime.valid) {
        return true;  // 从未同步过
    }
    
    unsigned long timeSinceSync = (millis() - lastSyncTime.receivedAt) / 1000;
    return timeSinceSync >= config.syncInterval;
}

bool NTPClient::getCurrentTime(TimeData& timeData) {
    if(!lastSyncTime.valid) {
        return false;
    }
    
    timeData = ntpToTimeData(lastSyncTime);
    return true;
}

void NTPClient::setAutoSync(bool enable) {
    config.autoSync = enable;
}

uint32_t NTPClient::getSyncInterval() const {
    return config.syncInterval;
}

void NTPClient::setSyncInterval(uint32_t interval) {
    config.syncInterval = interval;
}

uint32_t NTPClient::getTimeSinceLastSync() const {
    if(!lastSyncTime.valid) {
        return UINT32_MAX;
    }
    
    return (millis() - lastSyncTime.receivedAt) / 1000;
}

bool NTPClient::isTimeValid() const {
    return lastSyncTime.valid;
}
