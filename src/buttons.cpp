/**
 * VFD Clock - Button Handler Implementation
 * 按键处理实现
 */

#include "buttons.h"
#include "config.h"

ButtonHandler::ButtonHandler(VFDDriver* vfdDriver, ClockDisplay* clockDisplay) 
    : vfd(vfdDriver), display(clockDisplay) {
    
    // 初始化按键状态
    for(int i = 0; i < BUTTON_COUNT; i++) {
        lastPress[i] = 0;
        buttonStates[i] = BUTTON_RELEASED;
    }
}

void ButtonHandler::init() {
    // 初始化按键引脚
    pinMode(BUTTON1_PIN, INPUT_PULLUP);
    pinMode(BUTTON2_PIN, INPUT_PULLUP);
    pinMode(BUTTON3_PIN, INPUT_PULLUP);
}

bool ButtonHandler::readButton(ButtonType button) {
    switch(button) {
        case BUTTON_1:
            return digitalRead(BUTTON1_PIN) == LOW;
        case BUTTON_2:
            return digitalRead(BUTTON2_PIN) == LOW;
        case BUTTON_3:
            return digitalRead(BUTTON3_PIN) == LOW;
        default:
            return false;
    }
}

void ButtonHandler::handleBrightnessButton() {
    if(!vfd) return;
    
    uint8_t currentBrightness = vfd->getBrightness();
    uint8_t newBrightness = (currentBrightness + 1) % BRIGHTNESS_LEVELS;
    vfd->setBrightness(newBrightness);
    
    // 显示亮度等级
    char message[17];
    sprintf(message, " BRIGHTNESS: %d  ", newBrightness + 1);
    if(display) {
        display->showMessage(message, 1000);
    }
}

void ButtonHandler::handleModeButton() {
    if(display) {
        display->toggleMode();
        
        // 显示模式切换信息
        const char* modeText = (display->getMode() == MODE_TIME) ? "  TIME MODE   " : "  DATE MODE   ";
        display->showMessage(modeText, 1000);
    }
}

void ButtonHandler::handleReservedButton() {
    // 预留按键功能，可以在这里添加其他功能
    // 例如：设置时间、闹钟等
    if(display) {
        display->showMessage("  RESERVED    ", 1000);
    }
}

void ButtonHandler::check() {
    unsigned long now = millis();
    
    // 检查每个按键
    for(int i = 0; i < BUTTON_COUNT; i++) {
        ButtonType button = static_cast<ButtonType>(i);
        bool isPressed = readButton(button);
        
        // 防抖处理
        if(isPressed && (now - lastPress[i] > BUTTON_DEBOUNCE)) {
            lastPress[i] = now;
            buttonStates[i] = BUTTON_PRESSED;
            
            // 处理按键事件
            switch(button) {
                case BUTTON_1:
                    handleBrightnessButton();
                    break;
                case BUTTON_2:
                    handleModeButton();
                    break;
                case BUTTON_3:
                    handleReservedButton();
                    break;
            }
        } else if(!isPressed) {
            buttonStates[i] = BUTTON_RELEASED;
        }
    }
}

ButtonState ButtonHandler::getButtonState(ButtonType button) const {
    if(button < BUTTON_COUNT) {
        return buttonStates[button];
    }
    return BUTTON_RELEASED;
}
